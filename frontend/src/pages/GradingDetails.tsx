import React, { useEffect, useMemo, useState } from 'react';
import { useLocation, useNavigate, useParams } from 'react-router-dom';
import { GradingStatus } from '../types/aegisGrader';
import { ToastContainer } from 'react-toastify';
import {
    ArrowLeftIcon,
    ClockIcon,
    UsersIcon,
    ArrowTrendingUpIcon,
    MagnifyingGlassIcon,
} from '@heroicons/react/24/outline';
import {
    CheckCircleIcon as CheckCircleSolid,
} from '@heroicons/react/24/solid';
import { parseQuestionBreakdown } from '@/components/QuestionBreakdown';
import { parseEvaluationForGradingDetails, type ParsedEvaluation } from '@/utils/xmlEvaluationParser';
import { fetchEventSource } from '@microsoft/fetch-event-source';
import { useUser } from '@/contexts/userContext';

const BASE_URL = process.env.NODE_ENV === 'production'
    ? 'https://api.aegisscholar.com'
    : 'http://localhost:8080';

interface EvaluationParser {
    parseEvaluation: (evaluationResult: any) => ParsedEvaluation | null;
}

// --- Configuration for Different Data Formats ---
export const EVALUATION_CONFIG: Record<string, EvaluationParser> = {
    // Current format: Array with markdown/XML string
    CURRENT: {
        parseEvaluation: (evaluationResult: any): ParsedEvaluation | null => {
            return parseEvaluationForGradingDetails(evaluationResult);
        }
    },
    // Future format: Direct object (for easy migration)
    FUTURE_OBJECT: {
        parseEvaluation: (evaluationResult: any): ParsedEvaluation | null => {
            if (!evaluationResult?.evaluation) return null;
            return evaluationResult.evaluation;
        }
    }
};

// --- Constants ---
export const CONSTANTS = {
    TOAST_DELAY: 2000,
    DOWNLOAD_DELAY: 1000,
    CURRENT_FORMAT: 'CURRENT' as keyof typeof EVALUATION_CONFIG
};

// --- Type Definitions (Simplified) ---
interface GradingResult {
    questionNumber: number;
    maxMarks: number;
    marksAwarded: number;
    feedback: string;
}

interface AnswerSheetResult {
    id: string;
    studentName: string;
    rollNumber: string;
    totalMarks: number;
    maxMarks: number;
    percentage: number;
    results: GradingResult[];
    detailedBreakdown: any;
    pdfUrl?: string;
}


interface AnswerSheetData {
    id: string;
    studentName: string;
    rollNumber: string;
    pdfUrl?: string;
    evaluationResult?: any;
    status?: 'pending' | 'processing' | 'completed' | 'error';
    processedAt?: Date;
}

interface SubmissionData {
    id: string;
    status: GradingStatus | string;
    testDetails: {
        subject: string;
        className: string;
        date: string;
    };
    questionPaper?: { pdfUrl?: string };
    rubric?: { pdfUrl?: string };
    gradingProgress?: number;
    answerSheets: AnswerSheetData[];
}

// --- Helper Functions (Using Theme Classes) ---
const formatScore = (score: number | string | undefined): string => {
    const num = typeof score === 'string' ? parseFloat(score) : score;
    if (num === undefined || isNaN(num)) return '-';
    return num % 1 === 0 ? num.toString() : num.toFixed(1);
};

const getScoreColorClass = (percentage: number | undefined): string => {
    if (percentage === undefined || isNaN(percentage)) return "text-gray-500";
    if (percentage >= 80) return "text-success-600 dark:text-success-400";
    if (percentage >= 60) return "text-primary-600 dark:text-primary-400";
    if (percentage >= 40) return "text-warning-600 dark:text-warning-400";
    return "text-danger-600 dark:text-danger-400";
};

const getScoreBgClass = (percentage: number | undefined): string => {
    if (percentage === undefined || isNaN(percentage)) return "bg-gray-100 dark:bg-gray-800";
    if (percentage >= 80) return "bg-success-100 dark:bg-success-900/20";
    if (percentage >= 60) return "bg-primary-100 dark:bg-primary-900/20";
    if (percentage >= 40) return "bg-warning-100 dark:bg-warning-900/20";
    return "bg-danger-100 dark:bg-danger-900/20";
};

// Custom Progress Bar Component for Grading Status
const GradingProgressBar: React.FC<{
    value: number;
    status?: string;
    className?: string;
}> = ({ value, status, className = "" }) => {
    const getProgressBarColor = () => {
        switch (status) {
            case 'completed':
                return 'bg-success-600 dark:bg-success-400';
            case 'processing':
                return 'bg-primary';
            case 'error':
                return 'bg-destructive';
            case 'pending':
            default:
                return 'bg-muted-foreground/50';
        }
    };

    const getBackgroundColor = () => {
        switch (status) {
            case 'completed':
                return 'bg-success-100 dark:bg-success-900/20';
            case 'processing':
                return 'bg-primary/20';
            case 'error':
                return 'bg-destructive/20';
            case 'pending':
            default:
                return 'bg-secondary';
        }
    };

    return (
        <div className={`relative h-2 w-full overflow-hidden rounded-full ${getBackgroundColor()} ${className}`}>
            <div
                className={`h-full transition-all duration-500 ease-in-out rounded-full ${getProgressBarColor()} ${status === 'processing' ? 'animate-pulse' : ''
                    }`}
                style={{ width: `${Math.max(0, Math.min(100, value))}%` }}
            />
        </div>
    );
};

// --- Status Badge ---
const StatusBadge: React.FC<{ status: GradingStatus | string }> = ({ status }) => {
    const statusValue = typeof status === 'string' ? status.toUpperCase() : status;

    const isCompleted = statusValue === GradingStatus.COMPLETED || statusValue === 'COMPLETED';
    const isInProgress = statusValue === GradingStatus.IN_PROGRESS || statusValue === 'IN_PROGRESS' || statusValue === 'IN PROGRESS';
    const isPending = statusValue === GradingStatus.PENDING || statusValue === 'PENDING';
    const isFailed = statusValue === GradingStatus.FAILED || statusValue === 'FAILED';

    let classes = '';
    let icon = null;
    let displayStatus = '';

    if (isCompleted) {
        classes = 'bg-success-100 text-success-800 dark:bg-success-900/20 dark:text-success-400';
        icon = <CheckCircleSolid className="h-4 w-4 inline mr-1" />;
        displayStatus = 'COMPLETED';
    } else if (isInProgress) {
        classes = 'bg-primary-100 text-primary-800 dark:bg-primary-900/20 dark:text-primary-400';
        icon = <ClockIcon className="h-4 w-4 inline mr-1" />;
        displayStatus = 'IN PROGRESS';
    } else if (isFailed) {
        classes = 'bg-danger-100 text-danger-800 dark:bg-danger-900/20 dark:text-danger-400';
        icon = <ClockIcon className="h-4 w-4 inline mr-1" />;
        displayStatus = 'FAILED';
    } else if (isPending) {
        classes = 'bg-warning-100 text-warning-800 dark:bg-warning-900/20 dark:text-warning-400';
        icon = <ClockIcon className="h-4 w-4 inline mr-1" />;
        displayStatus = 'PENDING';
    } else {
        classes = 'bg-primary-100 text-primary-800 dark:bg-primary-900/20 dark:text-primary-400';
        icon = <ClockIcon className="h-4 w-4 inline mr-1" />;
        displayStatus = 'PENDING';
    }

    return (
        <div className={`px-2 sm:px-3 py-1 rounded-full text-xs sm:text-sm font-medium ${classes}`}>
            {icon}
            <span className="hidden xs:inline sm:inline">{displayStatus}</span>
            <span className="xs:hidden sm:hidden">{displayStatus.split(' ')[0]}</span>
        </div>
    );
};

// --- Student Card ---
const StudentCard: React.FC<{
    sheet: AnswerSheetData;
    onViewResults: (result: AnswerSheetResult) => void;
    formatResults: (evaluationResult: any) => AnswerSheetResult | null;
}> = ({ sheet, onViewResults, formatResults }) => {
    const parsedEvaluation = useMemo(() => {
        return EVALUATION_CONFIG[CONSTANTS.CURRENT_FORMAT].parseEvaluation(sheet.evaluationResult);
    }, [sheet.evaluationResult]);

    const totalMarks = parsedEvaluation ? parsedEvaluation.total_marks : undefined;
    const maxMarks = parsedEvaluation ? parsedEvaluation.maximum_possible_marks : undefined;

    // Calculate percentage if not provided or if it's 0
    let percentage = parsedEvaluation ? parsedEvaluation.percentage_score : undefined;
    if ((!percentage || percentage === 0) && totalMarks !== undefined && maxMarks !== undefined && maxMarks > 0) {
        percentage = Math.round((totalMarks / maxMarks) * 100);
    }

    const { user } = useUser();
    const [progress, setProgress] = useState<number>(0);
    const [status, setStatus] = useState<string>('');
    const isGrading = status === 'processing';
    const isCompleted = status === 'completed' || (sheet.evaluationResult && !status);
    const hasError = status === 'error';
    console.log("Progress: ", progress);
    useEffect(() => {
        // Only start SSE if sheet doesn't have evaluation result yet
        if (sheet.evaluationResult) {
            setStatus('completed');
            setProgress(100);
            return;
        }

        const abortController = new AbortController();
        let shouldStop = false;

        const startSSE = async () => {
            try {
                await fetchEventSource(`${BASE_URL}/api/aegisGrader/getCurrentProgress/${sheet.id}`, {
                    method: 'GET',
                    headers: {
                        'Authorization': `Bearer ${user?.accessToken}`
                    },
                    credentials: 'include',
                    signal: abortController.signal,

                    onmessage(event: any) {
                        if (shouldStop) return;

                        try {
                            const data = JSON.parse(event.data);
                            console.log('SSE data received:', data);

                            setProgress(data.progress || 0);
                            setStatus(data.status || '');

                            // Handle completion
                            if (data.status === 'completed') {
                                shouldStop = true;
                                setProgress(100);
                                setStatus('completed');
                                console.log('Grading completed, stopping SSE');
                                abortController.abort();
                                // Optionally refresh the page data here
                                setTimeout(() => {
                                    window.location.reload();
                                }, 1000);
                            }
                        } catch (error) {
                            console.error('Error parsing SSE data:', error);
                        }
                    },

                    onerror(err: any) {
                        if (shouldStop) return;
                        console.error('❌ SSE error:', err);
                        // Don't retry if we're stopping or if it's an abort
                        if (err.name === 'AbortError' || shouldStop) {
                            return;
                        }
                        // Set error state
                        setStatus('error');
                        shouldStop = true;
                        abortController.abort();
                    },

                    onclose() {
                        console.log('SSE connection closed');
                    },

                    // Disable automatic retries
                    openWhenHidden: false
                });
            } catch (error) {
                if (!shouldStop) {
                    console.error('Failed to start SSE:', error);
                }
            }
        };

        startSSE();

        return () => {
            shouldStop = true;
            console.log('Cleaning up SSE connection');
            abortController.abort();
        };
    }, []);

    return (
        <div className="flex flex-col gap-3 p-3 sm:p-4 border border-border dark:border-border rounded-lg hover:shadow-sm dark:hover:shadow-lg hover:border-primary/20 dark:hover:border-primary/30 transition-all duration-200">
            {/* Student Info Row */}
            <div className="flex items-center gap-3 flex-1 min-w-0">
                <div className={`w-10 h-10 sm:w-12 sm:h-12 rounded-full ${getScoreBgClass(percentage)} flex items-center justify-center font-medium text-xs sm:text-sm flex-shrink-0`}>
                    {sheet.studentName?.split(' ').map(n => n[0]).join('').toUpperCase() || '?'}
                </div>
                <div className="min-w-0 flex-1">
                    <p className="font-medium text-foreground text-sm sm:text-base truncate">{sheet.studentName || "N/A"}</p>
                    <p className="text-xs sm:text-sm text-muted-foreground">Roll: {sheet.rollNumber || "N/A"}</p>
                </div>
            </div>

            {/* Progress Bar */}
            <div className="w-full">
                <div className="flex items-center justify-between mb-1">
                    <span className={`text-xs font-medium ${hasError ? 'text-destructive' :
                        isCompleted ? 'text-success-600 dark:text-success-400' :
                            isGrading ? 'text-primary' :
                                'text-muted-foreground'
                        }`}>
                        {hasError ? 'Grading Failed' :
                            isCompleted ? 'Grading Complete' :
                                isGrading ? 'Grading in Progress' :
                                    'Pending Grading'}
                    </span>
                    <span className={`text-xs font-medium ${hasError ? 'text-destructive' :
                        isCompleted ? 'text-success-600 dark:text-success-400' :
                            isGrading ? 'text-primary' :
                                'text-muted-foreground'
                        }`}>{Math.round(progress)}%</span>
                </div>
                <GradingProgressBar
                    value={progress}
                    status={status}
                />
            </div>

            {/* Score and Action Row */}
            <div className="flex items-center justify-between gap-3 sm:gap-4">
                {parsedEvaluation ? (
                    <div className="text-left">
                        <p className={`font-semibold text-sm sm:text-base ${getScoreColorClass(percentage)}`}>
                            {formatScore(totalMarks)}/{formatScore(maxMarks)}
                        </p>
                        <p className={`text-xs sm:text-sm ${getScoreColorClass(percentage)}`}>
                            {formatScore(percentage)}%
                        </p>
                    </div>
                ) : (
                    <div className="text-left">
                        <p className="text-muted-foreground text-sm">
                            {hasError ? 'Error in grading' : 'Not graded yet'}
                        </p>
                    </div>
                )}

                {parsedEvaluation && (
                    <button
                        className="px-3 py-2 bg-primary text-primary-foreground rounded-md hover:bg-primary/90 transition-colors text-xs sm:text-sm whitespace-nowrap flex-shrink-0 min-h-[36px] sm:min-h-[40px]"
                        onClick={() => {
                            const result = formatResults(sheet.evaluationResult);
                            if (result) onViewResults(result);
                        }}
                    >
                        <span className="hidden sm:inline">View Details</span>
                        <span className="sm:hidden">Details</span>
                    </button>
                )}
            </div>
        </div>
    );
};


// --- Main Component ---
export const GradingDetails: React.FC = () => {
    const location = useLocation();
    const navigate = useNavigate();
    const { id: submissionId } = useParams<{ id: string }>();
    const [searchTerm, setSearchTerm] = useState('');
    // Handle navigation to question breakdown page
    const handleViewResults = (result: AnswerSheetResult) => {
        navigate(`/question-breakdown/${result.id}`, {
            state: { submissionData: result }
        });
    };

    const submission = useMemo(() => {
        const history = location.state?.testHistory as SubmissionData[] | undefined;
        return history?.find((sub: SubmissionData) => sub.id === submissionId);
    }, [location.state?.testHistory, submissionId]);

    // --- Usage in your existing component ---
    const enhancedFormatResults = useMemo(() => (evaluationResult: any): AnswerSheetResult | null => {
        const parsedEvaluation = EVALUATION_CONFIG[CONSTANTS.CURRENT_FORMAT].parseEvaluation(evaluationResult);
        if (!parsedEvaluation) return null;


        // Parse the detailed breakdown
        const detailedBreakdown = parseQuestionBreakdown(evaluationResult);
        console.log("detailed breakdown", detailedBreakdown);

        const sections = Array.isArray(parsedEvaluation.section) ? parsedEvaluation.section : [parsedEvaluation.section];
        const questions: GradingResult[] = sections.flatMap((section) => {
            const questionsInSection = Array.isArray(section.question) ? section.question : [section.question];
            return questionsInSection.map((q) => ({
                questionNumber: q.question_number || 0,
                maxMarks: q.marks_possible || 0,
                marksAwarded: q.marks_awarded || 0,
                feedback: q.feedback || ''
            }));
        });


        questions.sort((a, b) => a.questionNumber - b.questionNumber);


        const sheet = submission?.answerSheets.find(s => s.evaluationResult === evaluationResult);


        const totalMarks = parsedEvaluation.total_marks || 0;
        const maxMarks = parsedEvaluation.maximum_possible_marks || 0;
        let percentage = parsedEvaluation.percentage_score || 0;

        // Calculate percentage if not provided or if it's 0
        if (percentage === 0 && maxMarks > 0) {
            percentage = Math.round((totalMarks / maxMarks) * 100);
        }

        return {
            id: sheet?.id || submissionId || '',
            studentName: sheet?.studentName || 'Unknown Student',
            rollNumber: sheet?.rollNumber || 'N/A',
            totalMarks,
            maxMarks,
            percentage,
            results: questions,
            detailedBreakdown,
            pdfUrl: sheet?.pdfUrl
        };
    }, [submission?.answerSheets, submissionId]);


    const filteredSheets = useMemo(() => {
        if (!submission?.answerSheets) return [];
        return submission.answerSheets.filter(sheet =>
            (sheet.studentName?.toLowerCase() || '').includes(searchTerm.toLowerCase()) ||
            (sheet.rollNumber?.toLowerCase() || '').includes(searchTerm.toLowerCase())
        );
    }, [submission?.answerSheets, searchTerm]);


    const classStats = useMemo(() => {
        if (!submission) return null;


        const gradedSheets = submission.answerSheets?.filter(sheet => {
            const parsed = EVALUATION_CONFIG[CONSTANTS.CURRENT_FORMAT].parseEvaluation(sheet.evaluationResult);
            return parsed !== null;
        });


        if (!gradedSheets || gradedSheets.length === 0) return null;


        const scores = gradedSheets.map(sheet => {
            const parsed = EVALUATION_CONFIG[CONSTANTS.CURRENT_FORMAT].parseEvaluation(sheet.evaluationResult);
            if (!parsed) return 0;

            const totalMarks = parsed.total_marks || 0;
            const maxMarks = parsed.maximum_possible_marks || 0;
            let percentage = parsed.percentage_score || 0;

            // Calculate percentage if not provided or if it's 0
            if (percentage === 0 && maxMarks > 0) {
                percentage = Math.round((totalMarks / maxMarks) * 100);
            }

            return percentage;
        });


        const average = scores.reduce((sum, score) => sum + score, 0) / scores.length;
        const highest = Math.max(...scores);
        const lowest = Math.min(...scores);


        return {
            average,
            highest,
            lowest,
            totalStudents: gradedSheets.length,
            totalSubmissions: submission.answerSheets.length
        };
    }, [submission]);


    if (!submission) {
        return (
            <div className="flex items-center justify-center min-h-screen bg-background px-4">
                <div className="text-center">
                    <p className="text-muted-foreground mb-4">Submission not found</p>
                    <button onClick={() => navigate(-1)} className="text-primary hover:underline">
                        Go Back
                    </button>
                </div>
            </div>
        );
    }


    return (
        <div className="min-h-screen bg-background p-2 sm:p-4 pb-16">
            <ToastContainer position="top-right" autoClose={CONSTANTS.TOAST_DELAY} />


            <div className="space-y-3 sm:space-y-4 pt-2">
                {/* Header */}
                <div className="flex items-center justify-between gap-2 sm:gap-4">
                    <button
                        onClick={() => navigate(-1)}
                        className="flex items-center gap-1 sm:gap-2 p-2 rounded-md text-muted-foreground hover:bg-muted transition-all duration-200 min-h-[40px]"
                    >
                        <ArrowLeftIcon className="w-4 h-4 flex-shrink-0" />
                        <span className="text-sm font-medium">Back</span>
                    </button>
                    <StatusBadge status={submission.status} />
                </div>


                {/* Test Info */}
                <div className="bg-card dark:bg-card rounded-lg shadow-sm border border-border p-4 sm:p-6">
                    <h1 className="text-lg sm:text-xl lg:text-2xl font-bold font-['Space_Grotesk'] mb-2 text-foreground break-words">
                        {submission.testDetails.subject || 'Unnamed Test'}
                    </h1>
                    <p className="text-sm sm:text-base text-muted-foreground break-words">
                        {submission.testDetails.className} • {submission.testDetails.date}
                    </p>
                </div>


                <div className="grid grid-cols-1 lg:grid-cols-4 gap-3 sm:gap-4">
                    {/* Student List */}
                    <div className="lg:col-span-3 order-1 lg:order-1">
                        <div className="bg-card dark:bg-card rounded-lg shadow-sm border border-border overflow-hidden">
                            <div className="p-3 sm:p-4 border-b border-border">
                                <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-3 sm:gap-4">
                                    <h2 className="text-base sm:text-lg font-semibold flex items-center gap-2 text-foreground">
                                        <UsersIcon className="h-4 w-4 sm:h-5 sm:w-5 flex-shrink-0" />
                                        Students ({filteredSheets.length})
                                    </h2>
                                    <div className="relative w-full sm:w-auto sm:min-w-[240px]">
                                        <MagnifyingGlassIcon className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                                        <input
                                            type="text"
                                            placeholder="Search students..."
                                            className="w-full pl-10 pr-4 py-2 sm:py-2.5 border border-border rounded-md bg-background text-foreground placeholder:text-muted-foreground focus:border-primary focus:outline-none text-sm sm:text-base"
                                            value={searchTerm}
                                            onChange={(e) => setSearchTerm(e.target.value)}
                                        />
                                    </div>
                                </div>
                            </div>


                            <div className="p-3 sm:p-4 space-y-2 sm:space-y-3">
                                {filteredSheets.length === 0 ? (
                                    <div className="text-center py-8 text-muted-foreground">
                                        No students found
                                    </div>
                                ) : (
                                    filteredSheets.map((sheet) => (
                                        <StudentCard
                                            key={sheet.id}
                                            sheet={sheet}
                                            onViewResults={handleViewResults}
                                            formatResults={enhancedFormatResults}
                                        />
                                    ))
                                )}
                            </div>
                        </div>
                    </div>


                    {/* Stats Sidebar */}
                    <div className="space-y-4 sm:space-y-6 order-2 lg:order-2">
                        {classStats && (
                            <div className="bg-card dark:bg-card rounded-lg shadow-sm border border-border p-3 sm:p-4">
                                <h3 className="font-semibold mb-3 flex items-center gap-2 text-foreground text-sm sm:text-base">
                                    <ArrowTrendingUpIcon className="h-4 w-4 sm:h-5 sm:w-5 flex-shrink-0" />
                                    Class Stats
                                </h3>
                                <div className="grid grid-cols-2 sm:grid-cols-1 gap-3">
                                    <div>
                                        <p className="text-xs sm:text-sm text-muted-foreground">Average</p>
                                        <p className="font-semibold text-foreground text-sm sm:text-base">{classStats.average.toFixed(1)}%</p>
                                    </div>
                                    <div>
                                        <p className="text-xs sm:text-sm text-muted-foreground">Highest</p>
                                        <p className="font-semibold text-success-600 dark:text-success-400 text-sm sm:text-base">{classStats.highest.toFixed(1)}%</p>
                                    </div>
                                    <div>
                                        <p className="text-xs sm:text-sm text-muted-foreground">Lowest</p>
                                        <p className="font-semibold text-danger-600 dark:text-danger-400 text-sm sm:text-base">{classStats.lowest.toFixed(1)}%</p>
                                    </div>
                                    <div>
                                        <p className="text-xs sm:text-sm text-muted-foreground">Graded</p>
                                        <p className="font-semibold text-foreground text-sm sm:text-base">{classStats.totalStudents}/{classStats.totalSubmissions}</p>
                                    </div>
                                </div>
                            </div>
                        )}


                        {/* <div className="bg-card dark:bg-card rounded-lg shadow-sm border border-border p-3 sm:p-4">
                            <h3 className="font-semibold mb-3 text-foreground text-sm sm:text-base">Actions</h3>
                            <div className="space-y-2 sm:space-y-3">
                                <button
                                    className="w-full px-4 py-2.5 sm:py-3 bg-primary text-primary-foreground rounded-md hover:bg-primary/90 transition-colors disabled:opacity-50 disabled:cursor-not-allowed text-sm sm:text-base font-medium"
                                    disabled={!classStats}
                                >
                                    Download Results
                                </button>
                                <button
                                    className="w-full px-4 py-2.5 sm:py-3 border border-border rounded-md hover:bg-accent hover:text-accent-foreground transition-colors disabled:opacity-50 disabled:cursor-not-allowed text-sm sm:text-base font-medium"
                                    disabled={!classStats}
                                >
                                    Share Results
                                </button>
                            </div>
                        </div> */}
                    </div>
                </div>
            </div>
        </div>
    );
};


export default GradingDetails;
