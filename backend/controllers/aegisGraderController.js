import AegisGrader from "../models/AegisGrader.js";
import CreditService from "../services/creditService.js";
import { USAGE_TYPES } from "../models/CreditTransaction.js";
import PollTime from "../models/PollTime.js";

import S3 from 'aws-sdk/clients/s3.js';
import { randomUUID } from 'crypto';

const s3 = new S3({
    apiVersion: '2006-03-01',
    accessKeyId: process.env.AWS_ACCESS_KEY_ID,
    secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY,
    region: "ap-south-1",
    signatureVersion: 'v4',
});

export const getCurrentProgress = async(req, res) => {
  // current progress poll time
  try {
    const { jobId } = req.params;
    console.log("[Mehul] got jobid: ", jobId);

    // Set SSE headers
    res.setHeader('Content-Type', 'text/event-stream');
    res.setHeader('Cache-Control', 'no-cache');
    res.setHeader('Connection', 'keep-alive');

    res.flushHeaders?.();

    const sendUpdate = (data, status) => {
      console.log("[Mehul] sending update for jobId: ", jobId);
      const payload = {
        progress: data,
        status: status
      };
      res.write(`data: ${JSON.stringify(payload)}\n\n`);
    }
    const pollTime = await PollTime.findOne({jobId: jobId});
    if (pollTime) {
      sendUpdate(pollTime.currentProgress, pollTime.status);
    }

    const intervalId = setInterval(async () => {
      const newProgress = await PollTime.findOne({jobId: jobId});
      console.log("[Mehul] got new progress: ", newProgress);
      if (newProgress) {
        sendUpdate(newProgress.currentProgress, newProgress.status);
      }
    }, 5000);

    req.on('close', () => {
      console.log("[Mehul] closing the connection");
      clearInterval(intervalId);
      res.end();
    })
  } catch (error) {
    console.error("Error retrieving current progress for jobid: ", req.params.jobId, error);
    if (!res.headersSent) {
      res.status(500).end();
    } else res.end()
  }
}

export const getAllSubmissions = async (req, res) => {
    try {
        const { userId } = req.params;
        // Filter submissions by the user who created them (handle both old and new schema)
        const submissions = await AegisGrader.find({
            $or: [
                { "testDetails.createdBy": userId }, // New schema
                { createdBy: userId } // Old schema (fallback)
            ]
        }).sort({ createdAt: -1 });

        if (!submissions || submissions.length === 0) {
            return res.status(404).json({ message: "No submissions found" });
        }

        const finalSubmissions = submissions.map(submission => {
            // Use new processing status if available, otherwise fall back to legacy logic
            let status = 'PENDING';
            let processingStats = null;
            let creditInfo = null;

            if (submission.processingStats && submission.processingStats.overallStatus) {
                // New schema with processing stats
                status = submission.processingStats.overallStatus.toUpperCase();
                processingStats = {
                    totalAnswerSheets: submission.processingStats.totalAnswerSheets,
                    successfulEvaluations: submission.processingStats.successfulEvaluations,
                    failedEvaluations: submission.processingStats.failedEvaluations,
                    completedAt: submission.processingStats.completedAt,
                    overallStatus: submission.processingStats.overallStatus
                };

                creditInfo = {
                    totalCreditsCharged: submission.creditInfo?.totalCreditsCharged || 0,
                    creditsRefunded: submission.creditInfo?.creditsRefunded || 0,
                    needsRefund: (submission.processingStats.failedEvaluations || 0) >
                                (submission.creditInfo?.refundTransactionIds?.length || 0)
                };
            } else {
                // Legacy logic for old submissions
                const hasEvaluatedSheets = submission.answerSheets.some(sheet => sheet.evaluationResult);
                const allEvaluated = submission.answerSheets.every(sheet => sheet.evaluationResult);

                if (allEvaluated) {
                    status = 'COMPLETED';
                } else if (hasEvaluatedSheets) {
                    status = 'IN_PROGRESS';
                }
            }

            return {
                id: submission._id,
                testDetails: submission.testDetails,
                answerSheets: submission.answerSheets,
                questionPaper: submission.questionPaper,
                rubric: submission.rubric,
                createdAt: submission.createdAt,
                status,
                processingStats,
                creditInfo
            };
        });

        return res.status(200).json({
            message: "Submissions retrieved successfully",
            submissions: finalSubmissions
        });
    } catch (error) {
        console.error("Error retrieving submissions:", error);
        return res.status(500).json({
            message: "Internal Server Error",
            error: error.message || "Unknown error occurred"
        });
    }
};

export const getPresignedUrl = async (req, res) => {
    try {
        const { questionPaperFile, rubricFile, answerSheets, fileType, testDetails } = req.body;
        if ((!questionPaperFile & !rubricFile) || !answerSheets || !fileType) {
            return res.status(400).json({ message: "All File names and type are required" });
        }

        let uploadUrlQuestion;
        let uploadUrlRubric;
        let questionPaperKey;
        let rubricKey;

        let manifestInfo = { testDetails: testDetails, files: [] };

        console.log(`[Mehul] [INFO] got filename: ${questionPaperFile} and fileType: ${fileType}`);
        console.log(`[Mehul] [INFO] got s3 region: ${s3.config.region}`);
        console.log(`[Mehul] [INFO] got answer sheet names: ${answerSheets.join(",")}`);

        if (questionPaperFile) {
            questionPaperKey = `${questionPaperFile}-${randomUUID()}.pdf`;
            const questionFileManifest = {
                fileName: questionPaperFile,
                fileType: fileType,
                key: questionPaperKey,
                filePurpose: "question_paper",
                timestamp: Date.now(),
            }

            manifestInfo.files.push(questionFileManifest);

            const s3ParamsQuestion = {
                Bucket: process.env.UPLOAD_BUCKET_NAME,
                Key: questionPaperKey,
                Expires: 60, // URL valid for 60 seconds
                ContentType: fileType,
                ACL: 'private'
            }

            uploadUrlQuestion = await s3.getSignedUrl('putObject', s3ParamsQuestion);

        }

        if (rubricFile) {
            rubricKey = `${rubricFile}-${randomUUID()}.pdf`;
            console.log("[Mehul][INFO] got rubric key: ", rubricKey);

            const rubricFileManifest = {
                fileName: rubricFile,
                fileType: fileType,
                key: rubricKey,
                filePurpose: "rubric",
                timestamp: Date.now(),
            }

            manifestInfo.files.push(rubricFileManifest);
            const s3ParamsRubric = {
                Bucket: process.env.UPLOAD_BUCKET_NAME,
                Key: rubricKey,
                Expires: 60, // URL valid for 60 seconds
                ContentType: fileType,
                ACL: 'private'
            }
            uploadUrlRubric = await s3.getSignedUrl('putObject', s3ParamsRubric);
        }

        // create a presigned url for each answer sheet to upload to s3
        let answerSheetUrls = [];
        for (const sheet of answerSheets) {
            console.log("[Mehul][INFO] got sheet: ", sheet);
            const sheetKey = `${sheet.filename}-${randomUUID()}.pdf`;
            console.log("[Mehul][INFO] got sheet key: ", sheetKey);
            const answerSheetManifest = {
                fileName: sheet.filename,
                fileType: fileType,
                key: sheetKey,
                filePurpose: "answer_sheet",
                studentName: sheet.studentName,
                rollNumber: sheet.rollNumber,
                timestamp: Date.now(),
            }

            manifestInfo.files.push(answerSheetManifest);
            const s3ParamsSheet = {
                Bucket: process.env.UPLOAD_BUCKET_NAME,
                Key: sheetKey,
                Expires: 60, // URL valid for 60 seconds
                ContentType: fileType,
                ACL: 'private'
            }
            const uploadUrlSheet = await s3.getSignedUrl('putObject', s3ParamsSheet);
            answerSheetUrls.push({ sheetName: sheet.filename, uploadUrl: uploadUrlSheet, key: sheetKey });
        }

        const manifestKey = `manifest-${randomUUID()}.json`;
        const manifestParams = {
            Bucket: process.env.UPLOAD_BUCKET_NAME,
            Key: manifestKey,
            Expires: 60, // URL valid for 60 seconds
            ContentType: 'application/json',
            ACL: 'private'
        }

        const uploadUrlManifest = await s3.getSignedUrl('putObject', manifestParams);
        console.log("[Mehul][INFO] got manifest presigned: ", uploadUrlManifest);

        // Deduct credits after successful presigned URL generation
        try {
            const userId = req.user.id; // From credit check middleware
            const userType = req.user.type; // From credit check middleware
            const creditsToDeduct = answerSheets.length; // 1 credit per answer sheet
            const deductionResult = await CreditService.deductCredits(userId, userType, creditsToDeduct, {
                feature: USAGE_TYPES.AEGIS_GRADER,
                description: `AegisGrader submission for ${testDetails.subject} - ${testDetails.className}`,
                relatedId: manifestKey, // Use manifest key as related ID
                metadata: {
                    testDetails,
                    answerSheetCount: answerSheets.length,
                    manifestKey,
                    questionPaperKey,
                    rubricKey
                }
            });

            console.log(`Successfully deducted ${creditsToDeduct} credits from user ${userId}. New balance: ${deductionResult.newBalance}`);

            // Add credit information to manifest for Lambda to use for refunds
            manifestInfo.creditInfo = {
                totalCreditsCharged: creditsToDeduct,
                creditsRefunded: 0,
                originalTransactionId: deductionResult.transaction.transactionId,
                refundTransactionIds: []
            };

            return res.status(200).json({
                message: "Presigned URL generated successfully",
                uploadUrlQuestion: uploadUrlQuestion,
                uploadUrlRubric: uploadUrlRubric,
                uploadUrlSheet: answerSheetUrls,
                fileNames: [questionPaperKey, rubricKey],
                manifest: manifestInfo,
                uploadUrlManifest: uploadUrlManifest,
                creditsDeducted: creditsToDeduct,
                newCreditBalance: deductionResult.newBalance
            });

        } catch (creditError) {
            console.error("Error deducting credits:", creditError);
            // If credit deduction fails, we should not provide the presigned URLs
            // This prevents users from uploading without paying
            return res.status(400).json({
                message: "Credit deduction failed",
                error: creditError.message || "Unable to process credit deduction"
            });
        }
    } catch (error) {
        console.error("Error generating presigned url: ", error);
        return res.status(500).json({
            message: "Internal Server Error",
            error: error.message || "Unknown error occurred"
        });
    }

}

/**
 * Optional endpoint for Lambda to notify completion (if needed for monitoring)
 * Lambda handles everything directly including refunds
 */
export const handleProcessingResults = async (req, res) => {
    try {
        const { submissionId, processingStats } = req.body;

        console.log(`Processing completed for submission: ${submissionId}`);
        console.log(`Stats:`, processingStats);

        return res.status(200).json({
            message: "Processing notification received",
            submissionId,
            processingStats
        });

    } catch (error) {
        console.error("Error handling processing notification:", error);
        return res.status(500).json({
            message: "Internal Server Error",
            error: error.message || "Unknown error occurred"
        });
    }
};

/**
 * Simple refund endpoint for Lambda to call
 * Lambda provides credit info and failed sheet count
 */
export const processLambdaRefunds = async (req, res) => {
    try {
        const { creditInfo, failedSheetCount } = req.body;

        if (!creditInfo || typeof failedSheetCount !== 'number') {
            return res.status(400).json({
                message: "Missing required fields",
                error: "creditInfo and failedSheetCount are required"
            });
        }

        const refundResult = await CreditService.processLambdaRefunds(creditInfo, failedSheetCount);

        return res.status(200).json({
            message: "Refund processed successfully",
            ...refundResult
        });

    } catch (error) {
        console.error("Error processing Lambda refunds:", error);
        return res.status(500).json({
            message: "Internal Server Error",
            error: error.message || "Unknown error occurred"
        });
    }
};

/**
 * Manually process refunds for submissions with failed evaluations
 * This can be used to process refunds for existing submissions
 */
export const processRefunds = async (req, res) => {
    try {
        const { submissionId } = req.params;
        const userId = req.id; // From JWT verification

        let submissions = [];

        if (submissionId) {
            // Process refund for specific submission
            const submission = await AegisGrader.findById(submissionId);
            if (!submission) {
                return res.status(404).json({
                    message: "Submission not found",
                    error: "AegisGrader submission not found"
                });
            }

            // Verify user owns this submission
            if (submission.testDetails.createdBy !== userId) {
                return res.status(403).json({
                    message: "Access denied",
                    error: "You can only process refunds for your own submissions"
                });
            }

            submissions = [submission];
        } else {
            // Process refunds for all user's submissions that need refunds
            submissions = await AegisGrader.find({
                'testDetails.createdBy': userId,
                'processingStats.overallStatus': { $in: ['completed', 'partial_failure'] },
                'processingStats.failedEvaluations': { $gt: 0 },
                $expr: {
                    $gt: [
                        '$processingStats.failedEvaluations',
                        { $size: { $ifNull: ['$creditInfo.refundTransactionIds', []] } }
                    ]
                }
            });
        }

        if (submissions.length === 0) {
            return res.status(200).json({
                message: "No submissions require refunds",
                processedSubmissions: 0,
                totalRefunded: 0
            });
        }

        let totalRefunded = 0;
        let processedSubmissions = 0;
        const results = [];

        for (const submission of submissions) {
            try {
                // Calculate failed sheets that haven't been refunded yet
                const failedSheets = submission.answerSheets.filter(sheet => sheet.status === 'error');
                const alreadyRefundedCount = submission.creditInfo?.refundTransactionIds?.length || 0;
                const failedSheetsNeedingRefund = Math.max(0, failedSheets.length - alreadyRefundedCount);

                if (failedSheetsNeedingRefund > 0) {
                    // Use the simplified Lambda refund method
                    const creditInfo = {
                        userId: submission.testDetails.createdBy,
                        userType: 'Teacher', // Assuming Teacher for manual refunds
                        originalTransactionId: submission.creditInfo?.originalTransactionId
                    };

                    const refundResult = await CreditService.processLambdaRefunds(creditInfo, failedSheetsNeedingRefund);

                    if (refundResult.success && refundResult.refundAmount > 0) {
                        // Update the submission with refund info
                        submission.creditInfo.creditsRefunded = (submission.creditInfo.creditsRefunded || 0) + refundResult.refundAmount;
                        submission.creditInfo.refundTransactionIds.push(refundResult.transaction.transactionId);
                        await submission.save();

                        totalRefunded += refundResult.refundAmount;
                        processedSubmissions++;
                        results.push({
                            submissionId: submission._id,
                            refundAmount: refundResult.refundAmount,
                            failedSheetsCount: failedSheetsNeedingRefund
                        });
                    }
                }
            } catch (refundError) {
                console.error(`Error processing refund for submission ${submission._id}:`, refundError);
                results.push({
                    submissionId: submission._id,
                    error: refundError.message
                });
            }
        }

        return res.status(200).json({
            message: "Refund processing completed",
            processedSubmissions,
            totalRefunded,
            results
        });

    } catch (error) {
        console.error("Error processing refunds:", error);
        return res.status(500).json({
            message: "Internal Server Error",
            error: error.message || "Unknown error occurred"
        });
    }
};

